<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VerificationDocument extends Model
{
    use HasFactory;
    
    protected $table = 'verification_documents';
    
    const CREATED_AT = null;
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_UNDER_REVIEW = 'under_review';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    
    protected $fillable = [
        'employer_id',
        'document_type',
        'status',
        'submission_date',
        'verification_date',
        'notes',
        'verified_by',
        'current_history_id',
    ];

    /**
     * Get the employer that this document status belongs to.
     */
    public function employer(): BelongsTo
    {
        return $this->belongsTo(Employer::class, 'employer_id');
    }

    /**
     * Get all of the upload histories for this document.
     */
    public function histories(): Has<PERSON>any
    {
        return $this->hasMany(VerificationDocumentHistory::class, 'verification_document_id');
    }

    /**
     * Get the currently active/approved history record for this document.
     */
    public function currentHistory(): BelongsTo
    {
        return $this->belongsTo(VerificationDocumentHistory::class, 'current_history_id');
    }
}
