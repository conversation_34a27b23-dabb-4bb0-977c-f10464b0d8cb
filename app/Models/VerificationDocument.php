<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class VerificationDocument extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'verification_documents';

    /**
     * The name of the "created_at" column.
     *
     * @var string|null
     */
    const CREATED_AT = null;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'employer_id',
        'document_type',
        'status',
        'submission_date',
        'verification_date',
        'notes',
        'verified_by',
        'current_history_id',
    ];

    /**
     * Get the employer that this document status belongs to.
     */
    public function employer(): BelongsTo
    {
        return $this->belongsTo(Employer::class, 'employer_id');
    }

    /**
     * Get all of the upload histories for this document.
     */
    public function histories(): HasMany
    {
        return $this->hasMany(VerificationDocumentHistory::class, 'verification_document_id');
    }

    /**
     * Get the currently active/approved history record for this document.
     */
    public function currentHistory(): BelongsTo
    {
        return $this->belongsTo(VerificationDocumentHistory::class, 'current_history_id');
    }
}
