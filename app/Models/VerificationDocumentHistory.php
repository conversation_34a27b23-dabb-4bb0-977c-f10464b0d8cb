<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VerificationDocumentHistory extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'verification_documents_history';

    /**
     * The name of the "created_at" column.
     * Laravel akan men<PERSON> 'uploaded_at' jika di-set seperti ini.
     *
     * @var string|null
     */
    const CREATED_AT = 'uploaded_at';

    /**
     * The name of the "updated_at" column.
     *
     * @var string|null
     */
    const UPDATED_AT = null;


    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'verification_document_id',
        'file_path',
        'status_at_upload',
        'notes',
        'verified_by',
        'verified_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'verified_at' => 'datetime',
    ];

    /**
     * Get the parent verification document record.
     */
    public function verificationDocument(): BelongsTo
    {
        return $this->belongsTo(VerificationDocument::class, 'verification_document_id');
    }

    /**
     * Get the user who verified this document upload.
     */
    public function verifiedBy(): BelongsTo
    {
        // Asumsi model User ada di App\Models\User
        return $this->belongsTo(User::class, 'verified_by');
    }
}
