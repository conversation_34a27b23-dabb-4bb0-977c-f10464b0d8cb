<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VerificationDocumentHistory extends Model
{
    use HasFactory;
    
    protected $table = 'verification_documents_history';
    
    const CREATED_AT = 'uploaded_at';
    const UPDATED_AT = null;
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_UNDER_REVIEW = 'under_review';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    
    protected $fillable = [
        'verification_document_id',
        'file_path',
        'status_at_upload',
        'notes',
        'verified_by',
        'verified_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'verified_at' => 'datetime',
    ];

    /**
     * Get the parent verification document record.
     */
    public function verificationDocument(): BelongsTo
    {
        return $this->belongsTo(VerificationDocument::class, 'verification_document_id');
    }

    /**
     * Get the user who verified this document upload.
     */
    public function verifiedBy(): BelongsTo
    {
        // Asumsi model User ada di App\Models\User
        return $this->belongsTo(User::class, 'verified_by');
    }
}
