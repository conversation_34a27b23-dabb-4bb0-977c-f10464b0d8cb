<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Employer;
use App\Models\VerificationDocument;
use App\Models\VerificationDocumentHistory;
use Illuminate\Support\Str;

class EmployerVerification extends Component
{
    use WithPagination;

    // UI state properties
    public $search = '';
    public $statusFilter = 'all';
    public $showDocumentModal = false;
    public $selectedEmployer = null;
    public $selectedDocument = null;
    public $verificationAction = '';
    public $verificationNotes = '';
    public $flashEffect = false;

    // Verification status options
    public $statusOptions = [
        'all' => 'All Status',
        'pending' => 'Pending Verification',
        'approved' => 'Approved',
        'rejected' => 'Rejected',
        'incomplete' => 'Incomplete Documents'
    ];

    public function mount()
    {
        $this->resetPage();
    }

    public function render()
    {
        $employers = Employer::with(['documents', 'user'])
            ->when($this->search, function ($query) {
                $query->where('company_name', 'ILIKE', '%' . $this->search . '%')
                      ->orWhere('industry', 'ILIKE', '%' . $this->search . '%')
                      ->orWhereHas('user', function ($q) {
                          $q->where('email', 'ILIKE', '%' . $this->search . '%');
                      });
            })
            ->when($this->statusFilter !== 'all', function ($query) {
                switch ($this->statusFilter) {
                    case 'pending':
                        $query->whereHas('documents', function ($q) {
                            $q->where('status', 'pending');
                        });
                        break;
                    case 'approved':
                        $query->where('is_verified', true);
                        break;
                    case 'rejected':
                        $query->whereHas('documents', function ($q) {
                            $q->where('status', 'rejected');
                        });
                        break;
                    case 'incomplete':
                        $query->whereDoesntHave('documents')
                              ->orWhereHas('documents', function ($q) {
                                  $q->whereIn('status', ['incomplete', 'missing']);
                              });
                        break;
                }
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('livewire.employer-verification', [
            'employers' => $employers
        ])->layout('components.layouts.app', ['title' => 'Employer Verification']);
    }

    // Custom pagination methods
    public function goToPage($page)
    {
        $this->setPage($page);
    }

    public function goPreviousPage()
    {
        $this->previousPage();
    }

    public function goNextPage()
    {
        $this->nextPage();
    }

    public function showDocuments($employerId)
    {
        $this->selectedEmployer = Employer::with(['documents.histories', 'user'])->findOrFail($employerId);
        $this->showDocumentModal = true;
        $this->flashEffect = true;
        
        // Reset flash effect after animation completes
        $this->dispatch('reset-flash-effect');
    }

    public function closeDocumentModal()
    {
        $this->showDocumentModal = false;
        $this->selectedEmployer = null;
        $this->selectedDocument = null;
        $this->verificationAction = '';
        $this->verificationNotes = '';
        $this->flashEffect = false;
    }

    public function selectDocument($documentId)
    {
        $this->selectedDocument = VerificationDocument::with(['histories', 'employer'])->findOrFail($documentId);
        $this->verificationAction = '';
        $this->verificationNotes = '';
    }

    public function processVerification()
    {
        if (!$this->selectedDocument || !$this->verificationAction) {
            session()->flash('error', 'Please select a document and verification action.');
            return;
        }

        try {
            // Update document status
            $this->selectedDocument->update([
                'status' => $this->verificationAction,
                'verification_date' => now(),
                'notes' => $this->verificationNotes,
                'verified_by' => 1, // Assuming admin user ID is 1
            ]);

            // Create history record
            VerificationDocumentHistory::create([
                'verification_document_id' => $this->selectedDocument->id,
                'file_path' => $this->selectedDocument->currentHistory->file_path ?? '',
                'status_at_upload' => $this->verificationAction,
                'notes' => $this->verificationNotes,
                'verified_by' => 1,
                'verified_at' => now(),
            ]);

            // Update employer verification status if all documents are approved
            if ($this->verificationAction === 'approved') {
                $employer = $this->selectedDocument->employer;
                $allDocumentsApproved = $employer->documents()
                    ->where('status', '!=', 'approved')
                    ->count() === 0;
                
                if ($allDocumentsApproved) {
                    $employer->update(['is_verified' => true]);
                }
            } else {
                // If any document is rejected, mark employer as not verified
                $this->selectedDocument->employer->update(['is_verified' => false]);
            }

            session()->flash('message', 'Verification processed successfully.');
            $this->closeDocumentModal();

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while processing verification.');
        }
    }

    public function approveEmployer($employerId)
    {
        try {
            $employer = Employer::findOrFail($employerId);
            $employer->update(['is_verified' => true]);
            
            // Update all pending documents to approved
            $employer->documents()->where('status', 'pending')->update([
                'status' => 'approved',
                'verification_date' => now(),
                'verified_by' => 1,
            ]);

            session()->flash('message', 'Employer approved successfully.');
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while approving employer.');
        }
    }

    public function rejectEmployer($employerId)
    {
        try {
            $employer = Employer::findOrFail($employerId);
            $employer->update(['is_verified' => false]);
            
            // Update all pending documents to rejected
            $employer->documents()->where('status', 'pending')->update([
                'status' => 'rejected',
                'verification_date' => now(),
                'verified_by' => 1,
            ]);

            session()->flash('message', 'Employer rejected successfully.');
            $this->flashEffect = true;
            $this->dispatch('reset-flash-effect');

        } catch (\Exception $e) {
            session()->flash('error', 'An error occurred while rejecting employer.');
        }
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function getVerificationStatusProperty()
    {
        if (!$this->selectedEmployer) return 'Unknown';
        
        if ($this->selectedEmployer->is_verified) {
            return 'Verified';
        }
        
        $pendingCount = $this->selectedEmployer->documents()->where('status', 'pending')->count();
        $rejectedCount = $this->selectedEmployer->documents()->where('status', 'rejected')->count();
        $approvedCount = $this->selectedEmployer->documents()->where('status', 'approved')->count();
        
        if ($pendingCount > 0) return 'Pending Review';
        if ($rejectedCount > 0) return 'Rejected';
        if ($approvedCount > 0) return 'Partially Approved';
        
        return 'No Documents';
    }
}
